'use client';

import { useEffect, useRef } from 'react';
import Editor from '@monaco-editor/react';
import { Play, Save, Download, Copy, RotateCcw } from 'lucide-react';

interface CodeEditorProps {
  code: string;
  onChange: (value: string) => void;
}

export default function CodeEditor({ code, onChange }: CodeEditorProps) {
  const editorRef = useRef<any>(null);

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor;
    
    // 配置JavaScript语言特性
    monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.ES2020,
      allowNonTsExtensions: true,
      moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
      module: monaco.languages.typescript.ModuleKind.CommonJS,
      noEmit: true,
      esModuleInterop: true,
      jsx: monaco.languages.typescript.JsxEmit.React,
      reactNamespace: 'React',
      allowJs: true,
      typeRoots: ['node_modules/@types']
    });

    // 添加常用的青龙面板相关的代码补全
    monaco.languages.registerCompletionItemProvider('javascript', {
      provideCompletionItems: (model: any, position: any) => {
        const suggestions = [
          {
            label: 'axios',
            kind: monaco.languages.CompletionItemKind.Module,
            insertText: 'const axios = require("axios");',
            documentation: '导入axios HTTP客户端'
          },
          {
            label: 'console.log',
            kind: monaco.languages.CompletionItemKind.Function,
            insertText: 'console.log(${1:message});',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: '输出日志信息'
          },
          {
            label: 'qinglong-class',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: [
              'class ${1:ScriptName} {',
              '  constructor() {',
              '    this.name = "${2:脚本名称}";',
              '    this.version = "1.0.0";',
              '  }',
              '',
              '  async main() {',
              '    console.log("脚本开始执行...");',
              '    try {',
              '      ${3:// 在这里编写脚本逻辑}',
              '      console.log("脚本执行完成");',
              '    } catch (error) {',
              '      console.error("脚本执行失败:", error);',
              '    }',
              '  }',
              '}',
              '',
              'new ${1:ScriptName}().main();'
            ].join('\n'),
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: '创建青龙面板脚本类模板'
          }
        ];
        return { suggestions };
      }
    });
  };

  const handleRunCode = () => {
    console.log('运行代码:', code);
    // 这里可以添加代码执行逻辑
    alert('代码运行功能正在开发中...');
  };

  const handleSaveCode = () => {
    const blob = new Blob([code], { type: 'text/javascript' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'qinglong-script.js';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleCopyCode = async () => {
    try {
      await navigator.clipboard.writeText(code);
      alert('代码已复制到剪贴板');
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  const handleResetCode = () => {
    if (confirm('确定要重置代码吗？这将清除所有当前内容。')) {
      onChange(`// 青龙面板脚本模板
const axios = require('axios');

class QinglongScript {
  constructor() {
    this.name = '新脚本';
    this.version = '1.0.0';
  }

  async main() {
    console.log('脚本开始执行...');
    
    try {
      // 在这里编写你的脚本逻辑
      
      console.log('脚本执行完成');
    } catch (error) {
      console.error('脚本执行失败:', error);
    }
  }
}

// 执行脚本
new QinglongScript().main();`);
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* 工具栏 */}
      <div className="flex items-center justify-between p-4 border-b bg-gray-50 dark:bg-gray-700">
        <div className="flex items-center space-x-2">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            JavaScript 代码编辑器
          </h3>
        </div>
        <div className="flex items-center space-x-2">
          <button
            type="button"
            onClick={handleRunCode}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            <Play className="h-4 w-4 mr-1" />
            运行
          </button>
          <button
            type="button"
            onClick={handleSaveCode}
            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white dark:border-gray-500 dark:hover:bg-gray-500"
          >
            <Download className="h-4 w-4 mr-1" />
            下载
          </button>
          <button
            type="button"
            onClick={handleCopyCode}
            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white dark:border-gray-500 dark:hover:bg-gray-500"
          >
            <Copy className="h-4 w-4 mr-1" />
            复制
          </button>
          <button
            type="button"
            onClick={handleResetCode}
            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white dark:border-gray-500 dark:hover:bg-gray-500"
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            重置
          </button>
        </div>
      </div>

      {/* 编辑器区域 */}
      <div className="flex-1 min-h-[600px]">
        <Editor
          height="100%"
          defaultLanguage="javascript"
          value={code}
          onChange={(value) => onChange(value || '')}
          onMount={handleEditorDidMount}
          theme="vs-dark"
          options={{
            minimap: { enabled: true },
            fontSize: 14,
            lineNumbers: 'on',
            roundedSelection: false,
            scrollBeyondLastLine: false,
            automaticLayout: true,
            tabSize: 2,
            insertSpaces: true,
            wordWrap: 'on',
            suggestOnTriggerCharacters: true,
            acceptSuggestionOnEnter: 'on',
            quickSuggestions: true,
            parameterHints: { enabled: true },
            formatOnPaste: true,
            formatOnType: true,
          }}
        />
      </div>
    </div>
  );
}
