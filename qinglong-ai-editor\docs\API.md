# API 文档

青龙面板 AI 脚本编辑器的 API 接口文档。

## 概述

本文档描述了编辑器的内部 API 接口和组件使用方法。

## 组件 API

### CodeEditor 组件

代码编辑器组件，基于 Monaco Editor 实现。

#### Props

```typescript
interface CodeEditorProps {
  code: string;           // 当前代码内容
  onChange: (value: string) => void;  // 代码变化回调
}
```

#### 使用示例

```tsx
import CodeEditor from '@/components/CodeEditor';

function MyComponent() {
  const [code, setCode] = useState('console.log("Hello World");');
  
  return (
    <CodeEditor 
      code={code} 
      onChange={setCode} 
    />
  );
}
```

#### 功能特性

- **语法高亮**: 支持 JavaScript 语法高亮
- **智能补全**: 提供青龙面板相关的代码补全
- **错误检测**: 实时语法错误检查
- **代码格式化**: 自动代码格式化
- **主题支持**: 支持明亮和暗黑主题

#### 内置补全项

编辑器提供以下内置代码补全：

1. **axios 导入**
   ```javascript
   const axios = require('axios');
   ```

2. **console.log 快捷输入**
   ```javascript
   console.log(message);
   ```

3. **青龙脚本类模板**
   ```javascript
   class ScriptName {
     constructor() {
       this.name = "脚本名称";
       this.version = "1.0.0";
     }
     
     async main() {
       // 脚本逻辑
     }
   }
   ```

### AIAssistant 组件

AI 助手组件，提供智能代码生成和优化建议。

#### Props

```typescript
interface AIAssistantProps {
  code: string;                    // 当前代码内容
  onCodeUpdate: (code: string) => void;  // 代码更新回调
}
```

#### 使用示例

```tsx
import AIAssistant from '@/components/AIAssistant';

function MyComponent() {
  const [code, setCode] = useState('');
  
  return (
    <AIAssistant 
      code={code} 
      onCodeUpdate={setCode} 
    />
  );
}
```

#### 功能特性

- **智能对话**: 支持自然语言交互
- **代码生成**: 根据描述生成代码
- **代码优化**: 提供优化建议
- **错误修复**: 自动检测和修复错误
- **快捷操作**: 预设常用操作按钮

#### 快捷操作

1. **生成脚本**: 生成青龙面板脚本模板
2. **修复错误**: 检查并修复代码错误
3. **优化代码**: 提供性能和可读性优化
4. **解释代码**: 解释代码功能和原理

#### 消息格式

```typescript
interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}
```

### ScriptTemplates 组件

脚本模板组件，提供预置的脚本模板。

#### Props

```typescript
interface ScriptTemplatesProps {
  onSelectTemplate: (code: string) => void;  // 模板选择回调
}
```

#### 使用示例

```tsx
import ScriptTemplates from '@/components/ScriptTemplates';

function MyComponent() {
  const handleSelectTemplate = (code: string) => {
    console.log('Selected template:', code);
  };
  
  return (
    <ScriptTemplates onSelectTemplate={handleSelectTemplate} />
  );
}
```

#### 模板数据结构

```typescript
interface Template {
  id: string;          // 模板唯一标识
  name: string;        // 模板名称
  description: string; // 模板描述
  category: string;    // 模板分类
  code: string;        // 模板代码
  tags: string[];      // 标签列表
}
```

#### 内置模板分类

1. **checkin**: 签到脚本
2. **monitor**: 监控脚本
3. **utility**: 工具脚本

## 工具函数

### 代码生成器

```typescript
// 生成基础脚本结构
function generateBasicScript(name: string, version: string): string;

// 生成签到脚本
function generateCheckInScript(config: CheckInConfig): string;

// 生成监控脚本
function generateMonitorScript(config: MonitorConfig): string;
```

### 代码分析器

```typescript
// 分析代码结构
function analyzeCode(code: string): CodeAnalysis;

// 检测代码错误
function detectErrors(code: string): Error[];

// 提取代码信息
function extractCodeInfo(code: string): CodeInfo;
```

## 配置接口

### 编辑器配置

```typescript
interface EditorConfig {
  theme: 'light' | 'dark';
  fontSize: number;
  tabSize: number;
  wordWrap: boolean;
  minimap: boolean;
  lineNumbers: boolean;
}
```

### AI 配置

```typescript
interface AIConfig {
  apiUrl?: string;
  apiKey?: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
}
```

## 事件系统

### 编辑器事件

```typescript
// 代码变化事件
editor.onDidChangeModelContent((event) => {
  console.log('Code changed:', event);
});

// 光标位置变化事件
editor.onDidChangeCursorPosition((event) => {
  console.log('Cursor position:', event.position);
});

// 选择变化事件
editor.onDidChangeCursorSelection((event) => {
  console.log('Selection changed:', event.selection);
});
```

### 自定义事件

```typescript
// 代码保存事件
window.addEventListener('code-save', (event) => {
  console.log('Code saved:', event.detail.code);
});

// 模板选择事件
window.addEventListener('template-select', (event) => {
  console.log('Template selected:', event.detail.template);
});

// AI 响应事件
window.addEventListener('ai-response', (event) => {
  console.log('AI response:', event.detail.response);
});
```

## 错误处理

### 错误类型

```typescript
enum ErrorType {
  SYNTAX_ERROR = 'syntax_error',
  RUNTIME_ERROR = 'runtime_error',
  NETWORK_ERROR = 'network_error',
  AI_ERROR = 'ai_error'
}

interface AppError {
  type: ErrorType;
  message: string;
  code?: string;
  details?: any;
}
```

### 错误处理示例

```typescript
try {
  const result = await generateCode(prompt);
  return result;
} catch (error) {
  if (error instanceof NetworkError) {
    showNotification('网络连接失败，请检查网络设置');
  } else if (error instanceof AIError) {
    showNotification('AI 服务暂时不可用，请稍后重试');
  } else {
    showNotification('发生未知错误，请联系技术支持');
  }
  throw error;
}
```

## 扩展开发

### 添加新模板

```typescript
// 1. 定义模板
const newTemplate: Template = {
  id: 'custom-template',
  name: '自定义模板',
  description: '模板描述',
  category: 'custom',
  code: '// 模板代码',
  tags: ['自定义', '示例']
};

// 2. 注册模板
registerTemplate(newTemplate);
```

### 添加新的 AI 功能

```typescript
// 1. 定义 AI 处理器
const customAIHandler = {
  name: 'custom-handler',
  description: '自定义 AI 功能',
  handler: async (input: string, context: any) => {
    // 处理逻辑
    return result;
  }
};

// 2. 注册处理器
registerAIHandler(customAIHandler);
```

### 自定义编辑器插件

```typescript
// 1. 创建插件
const customPlugin = {
  name: 'custom-plugin',
  activate: (editor: monaco.editor.IStandaloneCodeEditor) => {
    // 插件激活逻辑
  },
  deactivate: () => {
    // 插件停用逻辑
  }
};

// 2. 注册插件
registerEditorPlugin(customPlugin);
```

## 性能优化

### 代码分割

```typescript
// 动态导入组件
const CodeEditor = lazy(() => import('@/components/CodeEditor'));
const AIAssistant = lazy(() => import('@/components/AIAssistant'));
```

### 缓存策略

```typescript
// 模板缓存
const templateCache = new Map<string, Template>();

// AI 响应缓存
const aiResponseCache = new Map<string, string>();
```

### 防抖处理

```typescript
// 代码变化防抖
const debouncedOnChange = useMemo(
  () => debounce((value: string) => {
    onChange(value);
  }, 300),
  [onChange]
);
```

## 安全考虑

### 代码执行安全

- 不直接执行用户代码
- 使用沙箱环境进行代码分析
- 过滤危险的代码模式

### 数据安全

- 本地存储敏感信息加密
- API 请求使用 HTTPS
- 用户输入验证和清理

### XSS 防护

```typescript
// 清理用户输入
function sanitizeInput(input: string): string {
  return DOMPurify.sanitize(input);
}

// 安全渲染代码
function renderCode(code: string): string {
  return hljs.highlight(sanitizeInput(code), { language: 'javascript' }).value;
}
```
