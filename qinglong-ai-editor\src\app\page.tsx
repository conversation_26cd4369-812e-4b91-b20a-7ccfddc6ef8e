'use client';

import { useState } from 'react';
import CodeEditor from '@/components/CodeEditor';
import AIAssistant from '@/components/AIAssistant';
import ScriptTemplates from '@/components/ScriptTemplates';
import { Bot, Code, FileText, Settings } from 'lucide-react';

export default function Home() {
  const [activeTab, setActiveTab] = useState('editor');
  const [code, setCode] = useState(`// 青龙面板脚本示例
// 这是一个基础的签到脚本模板

const axios = require('axios');

class QinglongScript {
  constructor() {
    this.name = '示例脚本';
    this.version = '1.0.0';
  }

  async main() {
    console.log('脚本开始执行...');

    try {
      // 在这里编写你的脚本逻辑
      await this.checkIn();

      console.log('脚本执行完成');
    } catch (error) {
      console.error('脚本执行失败:', error);
    }
  }

  async checkIn() {
    // 示例：发送签到请求
    const response = await axios.get('https://api.example.com/checkin');
    console.log('签到结果:', response.data);
  }
}

// 执行脚本
new QinglongScript().main();`);

  const tabs = [
    { id: 'editor', label: '代码编辑器', icon: Code },
    { id: 'ai', label: 'AI助手', icon: Bot },
    { id: 'templates', label: '脚本模板', icon: FileText },
    { id: 'settings', label: '设置', icon: Settings },
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* 头部导航 */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Bot className="h-8 w-8 text-blue-600 mr-3" />
              <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                青龙面板 AI 脚本编辑器
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                v1.0.0
              </span>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* 标签页导航 */}
        <div className="mb-6">
          <nav className="flex space-x-8" aria-label="Tabs">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        {/* 标签页内容 */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
          {activeTab === 'editor' && (
            <CodeEditor code={code} onChange={setCode} />
          )}
          {activeTab === 'ai' && (
            <AIAssistant code={code} onCodeUpdate={setCode} />
          )}
          {activeTab === 'templates' && (
            <ScriptTemplates onSelectTemplate={setCode} />
          )}
          {activeTab === 'settings' && (
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                编辑器设置
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                设置功能正在开发中...
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
