'use client';

import { useState } from 'react';
import { FileText, Download, Eye, Search } from 'lucide-react';

interface ScriptTemplatesProps {
  onSelectTemplate: (code: string) => void;
}

interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  code: string;
  tags: string[];
}

export default function ScriptTemplates({ onSelectTemplate }: ScriptTemplatesProps) {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);

  const templates: Template[] = [
    {
      id: 'basic-checkin',
      name: '基础签到脚本',
      description: '通用的签到脚本模板，支持Cookie认证和通知推送',
      category: 'checkin',
      tags: ['签到', '基础', '通用'],
      code: `// 基础签到脚本模板
const axios = require('axios');
const { sendNotify } = require('./sendNotify');

class CheckInScript {
  constructor() {
    this.name = '基础签到脚本';
    this.version = '1.0.0';
    this.baseURL = 'https://api.example.com';
  }

  async main() {
    console.log(\`开始执行 \${this.name}\`);
    
    try {
      // 检查必要的环境变量
      if (!process.env.COOKIE) {
        throw new Error('请设置COOKIE环境变量');
      }

      // 执行签到
      const result = await this.checkIn();
      
      // 发送通知
      await this.notify(result);
      
      console.log('签到完成');
    } catch (error) {
      console.error('签到失败:', error.message);
      await sendNotify(this.name, \`签到失败: \${error.message}\`);
    }
  }

  async checkIn() {
    const response = await axios.post(\`\${this.baseURL}/checkin\`, {}, {
      headers: {
        'Cookie': process.env.COOKIE,
        'User-Agent': 'Mozilla/5.0 (compatible; QinglongScript/1.0)',
        'Content-Type': 'application/json'
      }
    });

    if (response.data.code === 0) {
      return {
        success: true,
        message: response.data.message || '签到成功',
        data: response.data.data
      };
    } else {
      throw new Error(response.data.message || '签到失败');
    }
  }

  async notify(result) {
    const message = \`\${this.name}\\n\${result.message}\`;
    await sendNotify(this.name, message);
  }
}

// 执行脚本
new CheckInScript().main();`
    },
    {
      id: 'advanced-checkin',
      name: '高级签到脚本',
      description: '支持多账号、重试机制和详细日志的签到脚本',
      category: 'checkin',
      tags: ['签到', '多账号', '重试'],
      code: `// 高级签到脚本模板
const axios = require('axios');
const { sendNotify } = require('./sendNotify');

class AdvancedCheckInScript {
  constructor() {
    this.name = '高级签到脚本';
    this.version = '2.0.0';
    this.baseURL = 'https://api.example.com';
    this.maxRetries = 3;
    this.retryDelay = 2000;
  }

  async main() {
    console.log(\`开始执行 \${this.name} v\${this.version}\`);
    
    try {
      // 获取账号列表
      const accounts = this.getAccounts();
      if (accounts.length === 0) {
        throw new Error('未找到有效账号，请检查环境变量');
      }

      console.log(\`找到 \${accounts.length} 个账号\`);
      
      const results = [];
      
      // 遍历所有账号
      for (let i = 0; i < accounts.length; i++) {
        const account = accounts[i];
        console.log(\`\\n开始处理账号 \${i + 1}/\${accounts.length}\`);
        
        try {
          const result = await this.processAccount(account, i + 1);
          results.push(result);
          
          // 账号间延迟
          if (i < accounts.length - 1) {
            await this.sleep(3000);
          }
        } catch (error) {
          console.error(\`账号 \${i + 1} 处理失败:\`, error.message);
          results.push({
            account: i + 1,
            success: false,
            message: error.message
          });
        }
      }
      
      // 发送汇总通知
      await this.sendSummaryNotify(results);
      
    } catch (error) {
      console.error('脚本执行失败:', error.message);
      await sendNotify(this.name, \`执行失败: \${error.message}\`);
    }
  }

  getAccounts() {
    const cookies = process.env.COOKIES || process.env.COOKIE || '';
    return cookies.split('&').filter(cookie => cookie.trim());
  }

  async processAccount(cookie, accountIndex) {
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(\`账号 \${accountIndex} 第 \${attempt} 次尝试\`);
        
        const result = await this.checkIn(cookie);
        
        console.log(\`账号 \${accountIndex} 签到成功: \${result.message}\`);
        return {
          account: accountIndex,
          success: true,
          message: result.message,
          data: result.data
        };
        
      } catch (error) {
        console.error(\`账号 \${accountIndex} 第 \${attempt} 次尝试失败:\`, error.message);
        
        if (attempt < this.maxRetries) {
          console.log(\`等待 \${this.retryDelay}ms 后重试...\`);
          await this.sleep(this.retryDelay);
        } else {
          throw error;
        }
      }
    }
  }

  async checkIn(cookie) {
    const response = await axios.post(\`\${this.baseURL}/checkin\`, {}, {
      headers: {
        'Cookie': cookie,
        'User-Agent': 'Mozilla/5.0 (compatible; QinglongScript/2.0)',
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });

    if (response.data.code === 0) {
      return {
        success: true,
        message: response.data.message || '签到成功',
        data: response.data.data
      };
    } else {
      throw new Error(response.data.message || '签到失败');
    }
  }

  async sendSummaryNotify(results) {
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;
    
    let message = \`\${this.name} 执行完成\\n\`;
    message += \`成功: \${successCount}/\${totalCount}\\n\\n\`;
    
    results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      message += \`账号\${result.account}: \${status} \${result.message}\\n\`;
    });
    
    await sendNotify(this.name, message);
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 执行脚本
new AdvancedCheckInScript().main();`
    },
    {
      id: 'monitor-script',
      name: '商品监控脚本',
      description: '监控商品价格变化和库存状态',
      category: 'monitor',
      tags: ['监控', '商品', '价格'],
      code: `// 商品监控脚本模板
const axios = require('axios');
const { sendNotify } = require('./sendNotify');

class ProductMonitor {
  constructor() {
    this.name = '商品监控脚本';
    this.version = '1.0.0';
    this.products = this.getProductList();
  }

  async main() {
    console.log(\`开始执行 \${this.name}\`);
    
    try {
      if (this.products.length === 0) {
        throw new Error('未配置监控商品');
      }

      const alerts = [];
      
      for (const product of this.products) {
        try {
          const result = await this.checkProduct(product);
          if (result.shouldAlert) {
            alerts.push(result);
          }
        } catch (error) {
          console.error(\`检查商品 \${product.name} 失败:\`, error.message);
        }
        
        await this.sleep(1000); // 请求间隔
      }
      
      if (alerts.length > 0) {
        await this.sendAlerts(alerts);
      }
      
      console.log('监控完成');
    } catch (error) {
      console.error('监控失败:', error.message);
      await sendNotify(this.name, \`监控失败: \${error.message}\`);
    }
  }

  getProductList() {
    // 从环境变量读取商品配置
    const config = process.env.PRODUCTS || '[]';
    try {
      return JSON.parse(config);
    } catch {
      return [
        {
          id: 'example',
          name: '示例商品',
          url: 'https://api.example.com/product/123',
          targetPrice: 100,
          checkStock: true
        }
      ];
    }
  }

  async checkProduct(product) {
    console.log(\`检查商品: \${product.name}\`);
    
    const response = await axios.get(product.url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; ProductMonitor/1.0)'
      }
    });
    
    const data = response.data;
    const currentPrice = data.price;
    const inStock = data.stock > 0;
    
    let shouldAlert = false;
    let alertMessage = '';
    
    // 检查价格
    if (product.targetPrice && currentPrice <= product.targetPrice) {
      shouldAlert = true;
      alertMessage += \`价格降至 \${currentPrice} 元（目标: \${product.targetPrice} 元）\`;
    }
    
    // 检查库存
    if (product.checkStock && inStock) {
      shouldAlert = true;
      if (alertMessage) alertMessage += '，';
      alertMessage += '商品有库存';
    }
    
    return {
      product,
      currentPrice,
      inStock,
      shouldAlert,
      alertMessage
    };
  }

  async sendAlerts(alerts) {
    let message = \`\${this.name} 发现 \${alerts.length} 个商品变化:\\n\\n\`;
    
    alerts.forEach(alert => {
      message += \`📦 \${alert.product.name}\\n\`;
      message += \`💰 \${alert.alertMessage}\\n\\n\`;
    });
    
    await sendNotify(this.name, message);
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 执行脚本
new ProductMonitor().main();`
    },
    {
      id: 'data-collector',
      name: '数据收集脚本',
      description: '定期收集和存储数据的通用模板',
      category: 'utility',
      tags: ['数据', '收集', '存储'],
      code: `// 数据收集脚本模板
const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');
const { sendNotify } = require('./sendNotify');

class DataCollector {
  constructor() {
    this.name = '数据收集脚本';
    this.version = '1.0.0';
    this.dataDir = process.env.DATA_DIR || './data';
  }

  async main() {
    console.log(\`开始执行 \${this.name}\`);
    
    try {
      // 确保数据目录存在
      await this.ensureDataDir();
      
      // 收集数据
      const data = await this.collectData();
      
      // 保存数据
      await this.saveData(data);
      
      // 发送报告
      await this.sendReport(data);
      
      console.log('数据收集完成');
    } catch (error) {
      console.error('数据收集失败:', error.message);
      await sendNotify(this.name, \`收集失败: \${error.message}\`);
    }
  }

  async ensureDataDir() {
    try {
      await fs.access(this.dataDir);
    } catch {
      await fs.mkdir(this.dataDir, { recursive: true });
      console.log(\`创建数据目录: \${this.dataDir}\`);
    }
  }

  async collectData() {
    console.log('开始收集数据...');
    
    const sources = this.getDataSources();
    const collectedData = {
      timestamp: new Date().toISOString(),
      sources: {}
    };
    
    for (const source of sources) {
      try {
        console.log(\`收集来源: \${source.name}\`);
        const data = await this.fetchFromSource(source);
        collectedData.sources[source.name] = data;
        
        await this.sleep(1000); // 请求间隔
      } catch (error) {
        console.error(\`收集 \${source.name} 失败:\`, error.message);
        collectedData.sources[source.name] = { error: error.message };
      }
    }
    
    return collectedData;
  }

  getDataSources() {
    // 配置数据源
    return [
      {
        name: 'api_data',
        url: 'https://api.example.com/data',
        method: 'GET',
        headers: {
          'Authorization': process.env.API_TOKEN || ''
        }
      },
      {
        name: 'status_check',
        url: 'https://api.example.com/status',
        method: 'GET'
      }
    ];
  }

  async fetchFromSource(source) {
    const response = await axios({
      method: source.method,
      url: source.url,
      headers: {
        'User-Agent': 'DataCollector/1.0',
        ...source.headers
      },
      timeout: 10000
    });
    
    return {
      status: response.status,
      data: response.data,
      timestamp: new Date().toISOString()
    };
  }

  async saveData(data) {
    const filename = \`data_\${new Date().toISOString().split('T')[0]}.json\`;
    const filepath = path.join(this.dataDir, filename);
    
    // 读取现有数据
    let existingData = [];
    try {
      const content = await fs.readFile(filepath, 'utf8');
      existingData = JSON.parse(content);
    } catch {
      // 文件不存在，使用空数组
    }
    
    // 添加新数据
    existingData.push(data);
    
    // 保存数据
    await fs.writeFile(filepath, JSON.stringify(existingData, null, 2));
    console.log(\`数据已保存到: \${filepath}\`);
  }

  async sendReport(data) {
    const successCount = Object.values(data.sources).filter(s => !s.error).length;
    const totalCount = Object.keys(data.sources).length;
    
    let message = \`\${this.name} 执行报告\\n\`;
    message += \`时间: \${data.timestamp}\\n\`;
    message += \`成功: \${successCount}/\${totalCount}\\n\\n\`;
    
    Object.entries(data.sources).forEach(([name, result]) => {
      const status = result.error ? '❌' : '✅';
      const info = result.error ? result.error : '数据收集成功';
      message += \`\${status} \${name}: \${info}\\n\`;
    });
    
    await sendNotify(this.name, message);
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 执行脚本
new DataCollector().main();`
    }
  ];

  const categories = [
    { id: 'all', name: '全部模板' },
    { id: 'checkin', name: '签到脚本' },
    { id: 'monitor', name: '监控脚本' },
    { id: 'utility', name: '工具脚本' }
  ];

  const filteredTemplates = templates.filter(template => {
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    const matchesSearch = searchTerm === '' || 
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    return matchesCategory && matchesSearch;
  });

  const handleUseTemplate = (template: Template) => {
    onSelectTemplate(template.code);
    setSelectedTemplate(null);
  };

  return (
    <div className="h-full flex flex-col">
      {/* 头部 */}
      <div className="p-4 border-b bg-gray-50 dark:bg-gray-700">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          脚本模板库
        </h3>
        
        {/* 搜索和筛选 */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="搜索模板..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
            />
          </div>
          
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
          >
            {categories.map(category => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* 模板列表 */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredTemplates.map(template => (
            <div
              key={template.id}
              className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start justify-between mb-2">
                <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                  {template.name}
                </h4>
                <FileText className="h-5 w-5 text-gray-400" />
              </div>
              
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                {template.description}
              </p>
              
              <div className="flex flex-wrap gap-1 mb-3">
                {template.tags.map(tag => (
                  <span
                    key={tag}
                    className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded dark:bg-blue-900 dark:text-blue-200"
                  >
                    {tag}
                  </span>
                ))}
              </div>
              
              <div className="flex space-x-2">
                <button
                  type="button"
                  onClick={() => setSelectedTemplate(template)}
                  className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white dark:border-gray-500 dark:hover:bg-gray-500"
                >
                  <Eye className="h-4 w-4 mr-1" />
                  预览
                </button>
                <button
                  type="button"
                  onClick={() => handleUseTemplate(template)}
                  className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <Download className="h-4 w-4 mr-1" />
                  使用
                </button>
              </div>
            </div>
          ))}
        </div>
        
        {filteredTemplates.length === 0 && (
          <div className="text-center py-8">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">
              没有找到匹配的模板
            </p>
          </div>
        )}
      </div>

      {/* 模板预览弹窗 */}
      {selectedTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[80vh] flex flex-col">
            <div className="flex items-center justify-between p-4 border-b">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                {selectedTemplate.name}
              </h3>
              <button
                type="button"
                onClick={() => setSelectedTemplate(null)}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200"
              >
                ✕
              </button>
            </div>
            
            <div className="flex-1 overflow-y-auto p-4">
              <pre className="bg-gray-100 dark:bg-gray-900 p-4 rounded text-sm overflow-x-auto">
                <code>{selectedTemplate.code}</code>
              </pre>
            </div>
            
            <div className="flex justify-end space-x-2 p-4 border-t">
              <button
                type="button"
                onClick={() => setSelectedTemplate(null)}
                className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white dark:border-gray-500 dark:hover:bg-gray-500"
              >
                取消
              </button>
              <button
                type="button"
                onClick={() => handleUseTemplate(selectedTemplate)}
                className="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                使用此模板
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
