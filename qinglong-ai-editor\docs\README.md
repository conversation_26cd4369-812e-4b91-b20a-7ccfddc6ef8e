# 青龙面板 AI 脚本编辑器

一个基于 Web 的青龙面板脚本编辑器，集成了 AI 辅助功能，帮助用户更高效地编写和管理青龙面板自动化脚本。

## 🚀 功能特性

### 核心功能
- **代码编辑器**: 基于 Monaco Editor，支持 JavaScript 语法高亮和智能补全
- **AI 助手**: 智能代码生成、优化建议和错误修复
- **脚本模板**: 丰富的预置模板，涵盖签到、监控、数据收集等场景
- **实时预览**: 即时查看代码效果和语法检查

### 编辑器特性
- 语法高亮和代码折叠
- 智能代码补全
- 错误检测和提示
- 代码格式化
- 多主题支持（明亮/暗黑）
- 代码搜索和替换

### AI 辅助功能
- **代码生成**: 根据需求描述自动生成脚本代码
- **代码优化**: 分析现有代码并提供优化建议
- **错误修复**: 自动检测并修复常见错误
- **代码解释**: 详细解释代码功能和工作原理

### 脚本模板
- **基础签到脚本**: 通用签到模板，支持 Cookie 认证
- **高级签到脚本**: 多账号支持，重试机制，详细日志
- **商品监控脚本**: 价格监控，库存提醒
- **数据收集脚本**: 定期数据采集和存储

## 🛠️ 技术栈

- **前端框架**: Next.js 14 (React 18)
- **开发语言**: TypeScript
- **样式框架**: Tailwind CSS
- **代码编辑器**: Monaco Editor
- **图标库**: Lucide React
- **构建工具**: Vite

## 📦 安装和使用

### 环境要求
- Node.js 18.0 或更高版本
- npm 或 yarn 包管理器

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd qinglong-ai-editor
```

2. 安装依赖
```bash
npm install
# 或
yarn install
```

3. 启动开发服务器
```bash
npm run dev
# 或
yarn dev
```

4. 打开浏览器访问 `http://localhost:3000`

### 生产环境部署

1. 构建项目
```bash
npm run build
# 或
yarn build
```

2. 启动生产服务器
```bash
npm start
# 或
yarn start
```

## 📖 使用指南

### 基本使用

1. **选择功能模块**
   - 代码编辑器：编写和编辑脚本
   - AI 助手：获取 AI 帮助和建议
   - 脚本模板：选择预置模板
   - 设置：配置编辑器选项

2. **编写脚本**
   - 在代码编辑器中输入或粘贴代码
   - 使用智能补全功能提高编写效率
   - 利用语法检查确保代码正确性

3. **使用 AI 助手**
   - 点击快捷操作按钮获取常见帮助
   - 在聊天框中描述你的需求
   - AI 会提供代码建议和解决方案

4. **选择模板**
   - 浏览不同类别的脚本模板
   - 使用搜索功能快速找到需要的模板
   - 预览模板代码后选择使用

### 高级功能

#### 代码编辑器快捷键
- `Ctrl+S`: 保存代码
- `Ctrl+Z`: 撤销
- `Ctrl+Y`: 重做
- `Ctrl+F`: 搜索
- `Ctrl+H`: 替换
- `F11`: 全屏模式

#### AI 助手使用技巧
- 描述需求时尽量具体和详细
- 可以要求 AI 解释特定的代码片段
- 利用快捷操作快速获取常见帮助
- AI 生成的代码会自动应用到编辑器

#### 脚本模板定制
- 可以基于现有模板进行修改
- 支持保存自定义模板（功能开发中）
- 模板包含详细的注释和说明

## 🔧 配置说明

### 环境变量
项目支持以下环境变量配置：

```env
# API 配置（如果需要）
NEXT_PUBLIC_API_URL=https://api.example.com

# AI 服务配置（如果集成真实 AI 服务）
AI_API_KEY=your_ai_api_key
AI_API_URL=https://ai-api.example.com
```

### 编辑器配置
编辑器支持多种配置选项，可在设置页面进行调整：

- 主题选择（明亮/暗黑）
- 字体大小
- 缩进设置
- 自动保存
- 代码格式化选项

## 🤝 贡献指南

欢迎贡献代码和建议！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 开发规范
- 使用 TypeScript 编写代码
- 遵循 ESLint 规则
- 添加适当的注释和文档
- 确保代码测试通过

## 📝 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 基础代码编辑器功能
- AI 助手集成
- 脚本模板库
- 响应式设计

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Monaco Editor](https://microsoft.github.io/monaco-editor/) - 强大的代码编辑器
- [Next.js](https://nextjs.org/) - React 框架
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架
- [Lucide](https://lucide.dev/) - 图标库

## 📞 支持和反馈

如果你在使用过程中遇到问题或有建议，请：

1. 查看 [常见问题](docs/FAQ.md)
2. 搜索现有的 [Issues](../../issues)
3. 创建新的 Issue 描述问题
4. 联系开发团队

---

**注意**: 本工具仅用于学习和研究目的，请遵守相关网站的使用条款和法律法规。
