# 测试示例

本文档包含青龙面板 AI 脚本编辑器的测试示例和验证方法。

## 🧪 功能测试

### 代码编辑器测试

#### 基础功能测试

1. **代码输入测试**
```javascript
// 测试代码：在编辑器中输入以下代码
console.log('Hello, Qinglong!');

const testFunction = () => {
  return 'Editor is working!';
};

testFunction();
```

2. **语法高亮测试**
```javascript
// 测试各种语法高亮
const axios = require('axios');
const fs = require('fs');

class TestClass {
  constructor() {
    this.name = 'test';
  }
  
  async asyncMethod() {
    try {
      const response = await axios.get('https://api.example.com');
      return response.data;
    } catch (error) {
      console.error('Error:', error.message);
    }
  }
}
```

3. **代码补全测试**
- 输入 `console.` 应显示补全选项
- 输入 `axios.` 应显示 HTTP 方法补全
- 输入 `qinglong-class` 应生成类模板

#### 高级功能测试

1. **错误检测测试**
```javascript
// 故意包含语法错误的代码
const test = {
  name: 'test'
  value: 123  // 缺少逗号
};

function testFunction() {
  return undefined.property;  // 运行时错误
}
```

2. **代码格式化测试**
```javascript
// 格式混乱的代码
const test={name:'test',value:123};function testFunction(){return test.name;}
```

### AI 助手测试

#### 对话功能测试

1. **基础对话测试**
   - 输入："你好"
   - 预期：AI 回复问候并介绍功能

2. **代码生成测试**
   - 输入："请生成一个签到脚本"
   - 预期：生成包含签到逻辑的完整脚本

3. **代码解释测试**
   - 输入："请解释当前代码的功能"
   - 预期：详细解释代码结构和功能

4. **错误修复测试**
   - 输入："请检查代码中的错误"
   - 预期：识别并提供修复建议

#### 快捷操作测试

1. **生成脚本按钮**
   - 点击"生成脚本"按钮
   - 预期：自动生成脚本模板并更新编辑器

2. **修复错误按钮**
   - 在编辑器中输入有错误的代码
   - 点击"修复错误"按钮
   - 预期：提供错误修复建议

3. **优化代码按钮**
   - 点击"优化代码"按钮
   - 预期：提供代码优化建议

4. **解释代码按钮**
   - 点击"解释代码"按钮
   - 预期：解释当前代码功能

### 脚本模板测试

#### 模板浏览测试

1. **分类筛选测试**
   - 选择不同分类
   - 预期：显示对应分类的模板

2. **搜索功能测试**
   - 搜索"签到"
   - 预期：显示包含"签到"关键词的模板

3. **模板预览测试**
   - 点击任意模板的"预览"按钮
   - 预期：弹出模板代码预览窗口

4. **模板使用测试**
   - 点击任意模板的"使用"按钮
   - 预期：模板代码加载到编辑器

#### 模板代码验证

1. **基础签到脚本测试**
```javascript
// 验证基础签到脚本模板
const template = templates.find(t => t.id === 'basic-checkin');
console.assert(template.code.includes('CheckInScript'), '应包含签到类');
console.assert(template.code.includes('axios'), '应包含axios依赖');
console.assert(template.code.includes('sendNotify'), '应包含通知功能');
```

2. **高级签到脚本测试**
```javascript
// 验证高级签到脚本模板
const template = templates.find(t => t.id === 'advanced-checkin');
console.assert(template.code.includes('getAccounts'), '应包含多账号支持');
console.assert(template.code.includes('maxRetries'), '应包含重试机制');
console.assert(template.code.includes('sleep'), '应包含延迟功能');
```

## 🔧 集成测试

### 端到端测试流程

1. **完整工作流测试**
   ```
   1. 打开编辑器
   2. 选择一个脚本模板
   3. 使用 AI 助手优化代码
   4. 保存/下载代码
   5. 验证代码完整性
   ```

2. **跨组件交互测试**
   ```
   1. 在模板页面选择模板
   2. 切换到编辑器页面验证代码加载
   3. 切换到 AI 助手页面请求优化
   4. 返回编辑器验证代码更新
   ```

### 性能测试

1. **大文件处理测试**
```javascript
// 生成大量代码测试编辑器性能
const largeCode = Array(1000).fill(0).map((_, i) => 
  `console.log('Line ${i}');`
).join('\n');

// 测试编辑器是否能流畅处理大文件
```

2. **快速输入测试**
```javascript
// 模拟快速连续输入
const rapidInput = 'const test = "rapid typing test";'.split('');
rapidInput.forEach((char, index) => {
  setTimeout(() => {
    // 模拟输入字符
    editor.trigger('keyboard', 'type', { text: char });
  }, index * 10);
});
```

## 🌐 浏览器兼容性测试

### 测试矩阵

| 功能 | Chrome | Firefox | Safari | Edge |
|------|--------|---------|--------|------|
| 代码编辑器 | ✅ | ✅ | ✅ | ✅ |
| 语法高亮 | ✅ | ✅ | ✅ | ✅ |
| AI 助手 | ✅ | ✅ | ✅ | ✅ |
| 脚本模板 | ✅ | ✅ | ✅ | ✅ |
| 文件下载 | ✅ | ✅ | ✅ | ✅ |
| 剪贴板操作 | ✅ | ✅ | ⚠️ | ✅ |

### 移动端测试

1. **响应式布局测试**
   - 在不同屏幕尺寸下测试界面适配
   - 验证触摸操作的可用性

2. **移动端特定功能测试**
   - 虚拟键盘兼容性
   - 触摸滚动性能
   - 移动端浏览器兼容性

## 📊 自动化测试脚本

### Jest 测试示例

```javascript
// __tests__/components/CodeEditor.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import CodeEditor from '@/components/CodeEditor';

describe('CodeEditor', () => {
  test('renders code editor', () => {
    const mockOnChange = jest.fn();
    render(<CodeEditor code="" onChange={mockOnChange} />);
    
    expect(screen.getByText('JavaScript 代码编辑器')).toBeInTheDocument();
  });

  test('calls onChange when code changes', () => {
    const mockOnChange = jest.fn();
    render(<CodeEditor code="" onChange={mockOnChange} />);
    
    // 模拟代码输入
    // 注意：实际测试需要 Monaco Editor 的 mock
  });
});
```

### Cypress E2E 测试示例

```javascript
// cypress/e2e/editor.cy.js
describe('青龙面板 AI 脚本编辑器', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  it('应该加载主页面', () => {
    cy.contains('青龙面板 AI 脚本编辑器');
    cy.get('[data-testid="tab-editor"]').should('be.visible');
  });

  it('应该能够切换标签页', () => {
    cy.get('[data-testid="tab-ai"]').click();
    cy.contains('青龙面板 AI 助手');
    
    cy.get('[data-testid="tab-templates"]').click();
    cy.contains('脚本模板库');
  });

  it('应该能够使用脚本模板', () => {
    cy.get('[data-testid="tab-templates"]').click();
    cy.get('[data-testid="template-basic-checkin"]').within(() => {
      cy.get('button').contains('使用').click();
    });
    
    cy.get('[data-testid="tab-editor"]').click();
    cy.get('.monaco-editor').should('contain.text', 'CheckInScript');
  });
});
```

## 🐛 错误场景测试

### 网络错误测试

1. **离线状态测试**
   - 断开网络连接
   - 验证应用的离线功能
   - 检查错误提示是否友好

2. **API 错误测试**
   - 模拟 API 请求失败
   - 验证错误处理机制
   - 检查用户反馈

### 边界条件测试

1. **空代码测试**
   - 编辑器为空时的行为
   - AI 助手对空代码的处理

2. **超长代码测试**
   - 测试编辑器处理大文件的能力
   - 验证性能表现

3. **特殊字符测试**
   - 输入特殊字符和 Unicode
   - 验证编码处理正确性

## 📋 测试检查清单

### 功能测试检查清单

- [ ] 代码编辑器正常加载
- [ ] 语法高亮工作正常
- [ ] 代码补全功能可用
- [ ] 错误检测正确显示
- [ ] AI 助手响应正常
- [ ] 快捷操作按钮工作
- [ ] 脚本模板加载正确
- [ ] 模板搜索功能正常
- [ ] 文件下载功能可用
- [ ] 代码复制功能正常

### 用户体验检查清单

- [ ] 界面响应速度快
- [ ] 操作反馈及时
- [ ] 错误提示友好
- [ ] 移动端适配良好
- [ ] 键盘快捷键工作
- [ ] 无明显 UI 问题

### 兼容性检查清单

- [ ] Chrome 浏览器兼容
- [ ] Firefox 浏览器兼容
- [ ] Safari 浏览器兼容
- [ ] Edge 浏览器兼容
- [ ] 移动端浏览器兼容
- [ ] 不同屏幕尺寸适配

## 🚀 性能基准测试

### 加载性能

- 首次加载时间 < 3 秒
- Monaco Editor 初始化 < 2 秒
- 组件切换响应 < 500ms

### 运行时性能

- 代码输入延迟 < 100ms
- AI 响应时间 < 3 秒
- 模板加载时间 < 1 秒

### 内存使用

- 初始内存占用 < 50MB
- 长时间使用内存增长 < 100MB
- 无明显内存泄漏

---

通过以上测试确保青龙面板 AI 脚本编辑器的质量和稳定性。建议在每次发布前执行完整的测试流程。
