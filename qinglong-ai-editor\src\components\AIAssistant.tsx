'use client';

import { useState } from 'react';
import { Send, Bot, User, Lightbulb, Code, Bug, Zap } from 'lucide-react';

interface AIAssistantProps {
  code: string;
  onCodeUpdate: (code: string) => void;
}

interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

export default function AIAssistant({ code, onCodeUpdate }: AIAssistantProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: '你好！我是青龙面板AI助手。我可以帮助你：\n\n• 编写和优化青龙面板脚本\n• 解释代码功能\n• 修复代码错误\n• 提供脚本模板和示例\n\n请告诉我你需要什么帮助！',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const quickActions = [
    {
      icon: Code,
      label: '生成脚本',
      prompt: '请帮我生成一个青龙面板签到脚本模板'
    },
    {
      icon: Bug,
      label: '修复错误',
      prompt: '请帮我检查并修复当前代码中的错误'
    },
    {
      icon: Zap,
      label: '优化代码',
      prompt: '请帮我优化当前代码的性能和可读性'
    },
    {
      icon: Lightbulb,
      label: '解释代码',
      prompt: '请解释当前代码的功能和工作原理'
    }
  ];

  const handleSendMessage = async (message: string) => {
    if (!message.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: message,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    // 模拟AI响应
    setTimeout(() => {
      const aiResponse = generateAIResponse(message, code);
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiResponse.content,
        timestamp: new Date()
      };

      setMessages(prev => [...prev, aiMessage]);
      
      if (aiResponse.updatedCode) {
        onCodeUpdate(aiResponse.updatedCode);
      }
      
      setIsLoading(false);
    }, 1000 + Math.random() * 2000);
  };

  const generateAIResponse = (userMessage: string, currentCode: string) => {
    const message = userMessage.toLowerCase();
    
    if (message.includes('生成') || message.includes('创建') || message.includes('模板')) {
      return {
        content: '我为你生成了一个青龙面板脚本模板。这个模板包含了基本的结构和常用功能：\n\n• 使用类结构组织代码\n• 包含错误处理机制\n• 支持异步操作\n• 添加了日志输出\n\n你可以基于这个模板来开发你的具体功能。',
        updatedCode: `// 青龙面板脚本 - AI生成
const axios = require('axios');
const { sendNotify } = require('./sendNotify');

class QinglongScript {
  constructor() {
    this.name = 'AI生成的脚本';
    this.version = '1.0.0';
    this.author = 'AI助手';
  }

  async main() {
    console.log(\`开始执行 \${this.name} v\${this.version}\`);
    
    try {
      // 检查环境变量
      if (!process.env.COOKIE) {
        throw new Error('请设置COOKIE环境变量');
      }

      // 执行主要逻辑
      const result = await this.executeTask();
      
      // 发送通知
      await this.sendNotification(result);
      
      console.log('脚本执行完成');
    } catch (error) {
      console.error('脚本执行失败:', error.message);
      await sendNotify(this.name, \`执行失败: \${error.message}\`);
    }
  }

  async executeTask() {
    // 在这里实现你的具体逻辑
    console.log('执行任务中...');
    
    const response = await axios.get('https://api.example.com/task', {
      headers: {
        'Cookie': process.env.COOKIE,
        'User-Agent': 'Mozilla/5.0 (compatible; QinglongScript/1.0)'
      }
    });

    return response.data;
  }

  async sendNotification(result) {
    const message = \`\${this.name} 执行完成\\n结果: \${JSON.stringify(result)}\`;
    await sendNotify(this.name, message);
  }
}

// 执行脚本
new QinglongScript().main();`
      };
    }
    
    if (message.includes('修复') || message.includes('错误') || message.includes('bug')) {
      return {
        content: '我检查了你的代码，发现了一些可以改进的地方：\n\n• 添加了更完善的错误处理\n• 优化了异步操作的写法\n• 增加了输入验证\n• 改进了日志输出格式\n\n修复后的代码应该更加稳定和可靠。',
        updatedCode: currentCode.replace(/console\.log\(/g, 'console.log(`[${new Date().toISOString()}]`')
      };
    }
    
    if (message.includes('优化') || message.includes('性能')) {
      return {
        content: '我对你的代码进行了优化：\n\n• 添加了请求重试机制\n• 优化了错误处理逻辑\n• 改进了代码结构\n• 增加了性能监控\n\n这些优化将提高脚本的稳定性和执行效率。'
      };
    }
    
    if (message.includes('解释') || message.includes('说明')) {
      return {
        content: '让我解释一下当前代码的功能：\n\n这是一个青龙面板脚本模板，主要包含以下部分：\n\n• **类结构**: 使用ES6类来组织代码，便于维护\n• **构造函数**: 初始化脚本的基本信息\n• **main方法**: 脚本的主要执行逻辑\n• **错误处理**: 使用try-catch捕获和处理异常\n• **异步操作**: 使用async/await处理异步请求\n\n这种结构适合大多数青龙面板的自动化任务。'
      };
    }
    
    return {
      content: '我理解你的需求。作为青龙面板AI助手，我可以帮助你：\n\n• 编写各种类型的脚本（签到、抢购、监控等）\n• 优化现有代码的性能和稳定性\n• 解决代码中的错误和问题\n• 提供最佳实践建议\n\n请具体告诉我你想要实现什么功能，我会为你提供详细的代码和说明。'
    };
  };

  return (
    <div className="h-full flex flex-col">
      {/* AI助手头部 */}
      <div className="flex items-center justify-between p-4 border-b bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-600">
        <div className="flex items-center">
          <Bot className="h-6 w-6 text-blue-600 mr-2" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            青龙面板 AI 助手
          </h3>
        </div>
        <div className="text-sm text-gray-500 dark:text-gray-400">
          在线 • 随时为你服务
        </div>
      </div>

      {/* 快捷操作 */}
      <div className="p-4 border-b bg-gray-50 dark:bg-gray-700">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          {quickActions.map((action, index) => {
            const Icon = action.icon;
            return (
              <button
                key={index}
                type="button"
                onClick={() => handleSendMessage(action.prompt)}
                className="flex items-center p-2 text-sm text-gray-700 bg-white border border-gray-200 rounded-lg hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white dark:border-gray-500 dark:hover:bg-gray-500"
              >
                <Icon className="h-4 w-4 mr-2" />
                {action.label}
              </button>
            );
          })}
        </div>
      </div>

      {/* 消息列表 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.type === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-900 dark:bg-gray-600 dark:text-white'
              }`}
            >
              <div className="flex items-start">
                {message.type === 'ai' && (
                  <Bot className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                )}
                {message.type === 'user' && (
                  <User className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                )}
                <div className="whitespace-pre-wrap text-sm">{message.content}</div>
              </div>
              <div className="text-xs opacity-70 mt-1">
                {message.timestamp.toLocaleTimeString()}
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-gray-200 text-gray-900 dark:bg-gray-600 dark:text-white max-w-xs lg:max-w-md px-4 py-2 rounded-lg">
              <div className="flex items-center">
                <Bot className="h-4 w-4 mr-2" />
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 输入区域 */}
      <div className="p-4 border-t bg-white dark:bg-gray-800">
        <div className="flex space-x-2">
          <input
            type="text"
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage(inputMessage)}
            placeholder="输入你的问题或需求..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            disabled={isLoading}
          />
          <button
            type="button"
            onClick={() => handleSendMessage(inputMessage)}
            disabled={isLoading || !inputMessage.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}
