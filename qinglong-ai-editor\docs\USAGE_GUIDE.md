# 使用指南

欢迎使用青龙面板 AI 脚本编辑器！本指南将帮助你快速上手并充分利用编辑器的各项功能。

## 🎯 快速开始

### 1. 启动应用

确保你已经按照 [README.md](README.md) 中的说明安装并启动了应用。

访问 `http://localhost:3000` 即可开始使用。

### 2. 界面概览

应用主界面包含四个主要标签页：

- **代码编辑器** 📝: 编写和编辑 JavaScript 脚本
- **AI 助手** 🤖: 获取 AI 帮助和代码建议
- **脚本模板** 📋: 浏览和使用预置模板
- **设置** ⚙️: 配置编辑器选项（开发中）

## 📝 使用代码编辑器

### 基础操作

1. **编写代码**
   - 直接在编辑器中输入 JavaScript 代码
   - 享受语法高亮和自动缩进
   - 使用 `Ctrl+Space` 触发代码补全

2. **保存和导出**
   - 点击工具栏的 "下载" 按钮保存为 `.js` 文件
   - 使用 "复制" 按钮复制代码到剪贴板
   - 代码会自动保存在浏览器本地存储中

3. **代码操作**
   - **运行**: 点击 "运行" 按钮（当前为演示功能）
   - **重置**: 点击 "重置" 按钮恢复默认模板
   - **格式化**: 使用 `Alt+Shift+F` 格式化代码

### 智能功能

1. **代码补全**
   ```javascript
   // 输入以下内容会触发补全
   console.  // 显示 console 方法
   axios.    // 显示 HTTP 方法
   qinglong-class  // 生成脚本类模板
   ```

2. **错误检测**
   - 语法错误会用红色波浪线标出
   - 鼠标悬停查看错误详情
   - 实时检查代码问题

3. **快捷键**
   - `Ctrl+S`: 保存代码
   - `Ctrl+Z`: 撤销
   - `Ctrl+Y`: 重做
   - `Ctrl+F`: 搜索
   - `Ctrl+H`: 替换

## 🤖 使用 AI 助手

### 快捷操作

AI 助手提供四个快捷操作按钮：

1. **生成脚本** 📄
   - 自动生成青龙面板脚本模板
   - 包含最佳实践和错误处理
   - 代码会自动加载到编辑器

2. **修复错误** 🔧
   - 检查当前代码中的问题
   - 提供修复建议和改进方案
   - 自动应用修复（如果可能）

3. **优化代码** ⚡
   - 分析代码性能和可读性
   - 提供优化建议
   - 改进代码结构和效率

4. **解释代码** 💡
   - 详细解释代码功能
   - 说明工作原理和逻辑
   - 帮助理解复杂代码

### 自然语言交互

你可以通过聊天界面与 AI 助手交互：

**示例对话：**

```
用户: 我需要一个每日签到的脚本
AI: 我为你生成了一个签到脚本模板...

用户: 如何添加多账号支持？
AI: 你可以通过以下方式实现多账号...

用户: 这段代码有什么问题？
AI: 我检查了你的代码，发现以下问题...
```

### 使用技巧

1. **具体描述需求**
   ```
   ❌ "写个脚本"
   ✅ "写一个京东签到脚本，支持多账号和通知推送"
   ```

2. **提供上下文**
   ```
   ❌ "这里有错误"
   ✅ "第15行的axios请求返回undefined，应该如何处理？"
   ```

3. **分步骤询问**
   ```
   ✅ "先生成基础结构，然后添加错误处理，最后加上通知功能"
   ```

## 📋 使用脚本模板

### 浏览模板

1. **分类筛选**
   - 全部模板：查看所有可用模板
   - 签到脚本：各种签到相关模板
   - 监控脚本：商品监控、价格提醒等
   - 工具脚本：数据收集、通用工具等

2. **搜索功能**
   - 在搜索框中输入关键词
   - 支持搜索模板名称、描述和标签
   - 实时过滤显示结果

### 使用模板

1. **预览模板**
   - 点击 "预览" 按钮查看完整代码
   - 了解模板功能和结构
   - 确认是否符合需求

2. **应用模板**
   - 点击 "使用" 按钮
   - 模板代码会自动加载到编辑器
   - 可以基于模板进行修改

### 内置模板介绍

#### 1. 基础签到脚本
```javascript
// 适用场景：单账号简单签到
// 特点：结构清晰，易于理解和修改
// 包含：Cookie认证、错误处理、通知推送
```

#### 2. 高级签到脚本
```javascript
// 适用场景：多账号批量签到
// 特点：支持重试机制、详细日志
// 包含：多账号管理、指数退避、汇总报告
```

#### 3. 商品监控脚本
```javascript
// 适用场景：价格监控、库存提醒
// 特点：定时检查、条件触发
// 包含：价格比较、库存检测、即时通知
```

#### 4. 数据收集脚本
```javascript
// 适用场景：定期数据采集
// 特点：多数据源、本地存储
// 包含：数据获取、格式化、持久化
```

## 🛠️ 自定义和扩展

### 修改模板

1. **选择合适的模板**
   - 根据需求选择最接近的模板
   - 使用模板作为起点

2. **关键修改点**
   ```javascript
   // 修改API地址
   this.baseURL = 'https://your-api.com';
   
   // 修改请求头
   headers: {
     'Cookie': process.env.YOUR_COOKIE,
     'User-Agent': 'YourApp/1.0'
   }
   
   // 修改业务逻辑
   async yourCustomMethod() {
     // 你的自定义逻辑
   }
   ```

3. **测试和调试**
   - 使用 AI 助手检查代码
   - 添加详细的日志输出
   - 在青龙面板中测试运行

### 最佳实践

1. **代码结构**
   ```javascript
   class YourScript {
     constructor() {
       // 初始化配置
     }
     
     async main() {
       // 主要逻辑
     }
     
     async helperMethod() {
       // 辅助方法
     }
   }
   ```

2. **错误处理**
   ```javascript
   try {
     const result = await this.doSomething();
     console.log('成功:', result);
   } catch (error) {
     console.error('失败:', error.message);
     await this.handleError(error);
   }
   ```

3. **环境变量**
   ```javascript
   // 使用环境变量存储敏感信息
   const cookie = process.env.COOKIE;
   const token = process.env.API_TOKEN;
   ```

## 🔧 故障排除

### 常见问题

1. **编辑器无法加载**
   - 检查网络连接
   - 清除浏览器缓存
   - 尝试刷新页面

2. **代码补全不工作**
   - 确保使用支持的浏览器
   - 检查是否有JavaScript错误
   - 尝试重新加载页面

3. **AI助手无响应**
   - 当前版本使用模拟AI
   - 检查输入是否包含特殊字符
   - 尝试使用快捷操作按钮

4. **模板加载失败**
   - 检查网络连接
   - 确认模板数据完整性
   - 尝试选择其他模板

### 性能优化

1. **大文件处理**
   - 避免在编辑器中处理过大的文件
   - 使用代码分割和模块化
   - 定期清理不需要的代码

2. **浏览器优化**
   - 关闭不必要的浏览器标签
   - 确保有足够的内存
   - 使用现代浏览器版本

## 📚 学习资源

### 推荐学习路径

1. **JavaScript基础**
   - 变量、函数、对象
   - 异步编程（Promise、async/await）
   - 错误处理和调试

2. **Node.js相关**
   - 模块系统（require/import）
   - 文件系统操作
   - HTTP请求处理

3. **青龙面板特定**
   - 环境变量使用
   - 通知推送机制
   - 定时任务配置

### 实践建议

1. **从简单开始**
   - 使用基础模板学习结构
   - 逐步添加功能
   - 多练习和实验

2. **参考优秀代码**
   - 研究模板代码的实现
   - 学习最佳实践
   - 关注代码质量

3. **积极使用AI助手**
   - 遇到问题及时询问
   - 让AI解释不理解的代码
   - 利用AI优化代码质量

---

希望这个指南能帮助你更好地使用青龙面板 AI 脚本编辑器！如果有任何问题，请查看 [FAQ](FAQ.md) 或创建 Issue 反馈。
